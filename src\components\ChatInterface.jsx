"use client";
import React, { useState, useEffect, useRef } from "react";
import { SUGGESTION_CARDS } from "../utils/constants";
import { FaArrowUp, FaChevronLeft, FaChevronRight } from "react-icons/fa6";
import Image from "next/image";
import logo from "../assets/images/logo.png";

const ChatInterface = () => {
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([]);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  // State to track if current screen is mobile (< 768px width)
  const [isMobile, setIsMobile] = useState(false);
  const messagesEndRef = useRef(null);
  const carouselRef = useRef(null);
  const inputRef = useRef(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim()) {
      setMessages((prev) => [
        ...prev,
        { text: message.trim(), timestamp: Date.now() },
      ]);
      setMessage("");
    }
  };

  // Handle suggestion card click - automatically start conversation
  const handleSuggestionClick = (cardTitle) => {
    setMessages((prev) => [
      ...prev,
      { text: cardTitle, timestamp: Date.now() },
    ]);
    setMessage(""); // Clear input field
  };

  // Calculate maximum slides based on screen size
  // Mobile: Show 1 card per slide (total cards = total slides)
  // Tablet/Desktop: Show 2 cards per slide (total slides = cards / 2)
  const getMaxSlides = () => {
    if (isMobile) return SUGGESTION_CARDS.length;
    return Math.ceil(SUGGESTION_CARDS.length / 2);
  };
  const nextSlide = () => {
    const maxSlides = getMaxSlides();
    setCurrentSlide((prev) => (prev + 1) % maxSlides);
  };
  const prevSlide = () => {
    const maxSlides = getMaxSlides();
    setCurrentSlide((prev) => (prev - 1 + maxSlides) % maxSlides);
  };

  // Manual navigation functions
  const handleNextSlide = () => {
    nextSlide();
  };
  const handlePrevSlide = () => {
    prevSlide();
  };

  // Touch handlers for mobile/tablet swipe gestures on carousel
  const handleTouchStart = (e) => {
    setTouchStart(e.targetTouches[0].clientX);
  };
  const handleTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };
  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const swipeDistance = touchStart - touchEnd;
    const minSwipeDistance = 50; // Reduced from 75px for better responsiveness

    // Swipe left (next slide) - minimum 50px swipe distance
    if (swipeDistance > minSwipeDistance) {
      nextSlide();
    }
    // Swipe right (previous slide) - minimum 50px swipe distance
    if (swipeDistance < -minSwipeDistance) {
      prevSlide();
    }

    // Reset touch values
    setTouchStart(0);
    setTouchEnd(0);
  };

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  // Screen size detection and responsive behavior
  useEffect(() => {
    const checkMobile = () => {
      if (typeof window !== "undefined") {
        // Mobile breakpoint: < 768px (matches Tailwind's md breakpoint)
        setIsMobile(window.innerWidth < 768);
      }
    };

    checkMobile();

    const handleResize = () => {
      // Reset carousel to first slide when screen size changes
      setCurrentSlide(0);
      checkMobile();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("resize", handleResize);
      return () => window.removeEventListener("resize", handleResize);
    }
  }, []);

  // Auto-focus input on component mount and when returning to main page
  useEffect(() => {
    if (inputRef.current && messages.length === 0) {
      // Small delay to ensure the component is fully rendered
      const timer = setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [messages.length]);



  return (
    <div className=" bg-white flex flex-col">
      {/* DESKTOP LAYOUT (lg and above - ≥1024px) */}
      <div className="hidden lg:flex flex-1 flex-col px-4 py-7">
        {messages.length === 0 ? (
          /* DESKTOP: Initial state - centered layout with logo, title, input, and carousel */
          <div className="flex flex-col items-center justify-center flex-1">
            <div className="flex flex-col items-center w-[768px]">
              {/* DESKTOP: Business Logo and Name */}
              <div className="flex flex-col items-center mb-8">
                <Image
                  src={logo}
                  className="w-16 h-16  rounded-full object-cover  flex items-center justify-center mb-4"
                />
                <h2 className="text-2xl font-semibold text-gray-800 mb-2">
                  Arik photography
                </h2>
              </div>

              {/* DESKTOP: Main title */}
              <h1 className="text-4xl text-gray-900 mb-6 text-center">
                Ready when you are.
              </h1>

              {/* DESKTOP: Input form - centered, 768px width, 104px height */}
              <form onSubmit={handleSubmit} className="relative w-full mb-6">
                <input
                  ref={messages.length === 0 ? inputRef : null}
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Ask anything"
                  autoFocus={messages.length === 0}
                  className="pt-3 px-4 pb-16 pr-12 w-full h-[104px] text-black rounded-[20px] outline-none border border-[#E0E2D9] text-base shadow-md"
                />
                <button
                  type="submit"
                  className="absolute right-3 bottom-3 w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800"
                >
                  <FaArrowUp className="w-4 h-4" />
                </button>
              </form>

              {/* DESKTOP: Suggestion cards carousel - shows 2 cards per slide */}
              <div className="relative w-full max-w-2xl">
                <div className="overflow-hidden">
                  <div
                    ref={carouselRef}
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                  >
                    {/* DESKTOP: Create slides with 2 cards each */}
                    {Array.from({
                      length: Math.ceil(SUGGESTION_CARDS.length / 2),
                    }).map((_, slideIndex) => (
                      <div
                        key={slideIndex}
                        className="w-full flex-shrink-0 flex gap-4"
                      >
                        {/* DESKTOP: Show 2 cards side by side per slide */}
                        {SUGGESTION_CARDS.slice(
                          slideIndex * 2,
                          slideIndex * 2 + 2
                        ).map((card, cardIndex) => (
                          <div
                            key={slideIndex * 2 + cardIndex}
                            className="flex-1"
                          >
                            <button
                              onClick={() => handleSuggestionClick(card.title)}
                              className="w-full py-4 px-5 rounded-[12px] bg-[#f6f6f6] text-left group   transition-all duration-200"
                            >
                              <div className="text-[16px] font-[600] text-black mb-1">
                                {card.title}
                              </div>
                              <div className="text-[16px] text-gray-500">
                                {card.subtitle}
                              </div>
                            </button>
                          </div>
                        ))}
                      </div>
                    ))}
                  </div>
                </div>
                {/* DESKTOP: Navigation arrows - positioned outside carousel */}
                <button
                  onClick={handlePrevSlide}
                  className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-12 w-8 h-8 bg-white border border-gray-200 rounded- flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200"
                >
                  <FaChevronLeft className="w-3 h-3 text-gray-600" />
                </button>
                <button
                  onClick={handleNextSlide}
                  className="absolute right-0 top-1/2 transform -translate-y-1/2 translate-x-12 w-8 h-8 bg-white border border-gray-200 rounded-full flex items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200"
                >
                  <FaChevronRight className="w-3 h-3 text-gray-600" />
                </button>
              </div>
            </div>
          </div>
        ) : (
          /* DESKTOP: Chat state - messages displayed with fixed input at bottom */
          <>
            {/* DESKTOP: Messages container - scrollable area with bottom padding */}
            <div className="flex-1 space-y-4 overflow-y-auto pb-32">
              <div className="w-full lg:w-[768px] mx-auto">
                {messages.map((msg, index) => (
                  <div key={index} className="flex justify-end mb-6">
                    <div className="bg-gray-100 px-4 py-2 rounded-3xl max-w-xs lg:max-w-lg">
                      {msg.text}
                    </div>
                  </div>
                ))}
              </div>
              <div ref={messagesEndRef} />
            </div>

            {/* DESKTOP: Fixed input at bottom - 768px width, 104px height */}
            <div className="fixed bottom-0 left-0 right-0 p-4">
              <form
                onSubmit={handleSubmit}
                className="relative w-full lg:w-[768px] lg:h-[104px] mx-auto"
              >
                <input
                  ref={messages.length > 0 ? inputRef : null}
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Ask anything"
                  className="w-full h-[104px] pt-3 md:px-5 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-sm md:text-base shadow-lg"
                />
                <button
                  type="submit"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors duration-200"
                >
                  <FaArrowUp className="w-4 h-4" />
                </button>
              </form>
            </div>
          </>
        )}
      </div>

      {/* MOBILE & TABLET LAYOUT (below lg - <1024px) */}
      <div className="lg:hidden flex flex-col ">
        {messages.length === 0 ? (
          /* MOBILE/TABLET: Initial state - logo, title positioned above fixed bottom section */
          <div className="flex flex-col items-center justify-center flex-1 px-4 py-7 ">
            <div className="flex flex-col items-center w-full max-w-[803px]">
              {/* MOBILE ONLY: Business Logo and Name (hidden on tablet) */}
              <div className="flex flex-col items-center mb-8 md:hidden">
                <Image
                  src={logo}
                  className="w-12 h-12 object-cover rounded-full flex items-center justify-center mb-3"
                />
                <h2 className="text-xl font-semibold text-gray-800 mb-1">
                  Arik photography
                </h2>
              </div>

              {/* TABLET ONLY: Business Logo and Name (hidden on mobile and desktop) */}
              <div className="hidden md:flex lg:hidden flex-col items-center mb-8">
                <Image
                  src={logo}
                  className="w-14 h-14 rounded-full flex object-cover items-center justify-center mb-3"
                />
                <h2 className="text-xl font-semibold text-gray-800 mb-1">
                  Arik photography
                </h2>
              </div>
              {/* MOBILE/TABLET: Main title - responsive text size */}
              <h1 className="text-2xl md:text-4xl text-gray-900 mb-6 text-center">
                Ready when you are.
              </h1>
            </div>
          </div>
        ) : (
          /* MOBILE/TABLET: Chat state - messages with fixed input overlay */
          <>
            {/* MOBILE/TABLET: Messages container - scrollable with bottom padding */}
            <div className="flex-1 space-y-4 overflow-y-auto pb-32 ">
              <div className="w-full max-w-[803px] mx-auto px-4 py-8">
                {messages.map((msg, index) => (
                  <div key={index} className="flex justify-end mb-6">
                    <div className="bg-gray-100 px-4 py-2 rounded-3xl max-w-xs break-words">
                      {msg.text}
                    </div>
                  </div>
                ))}
              </div>
              <div ref={messagesEndRef} />
            </div>

            {/* MOBILE/TABLET: Fixed input overlay - 803px max width, 104px height */}
            <div className="fixed bottom-0 left-0 right-0  ">
              <form
                onSubmit={handleSubmit}
                className="relative w-full max-w-[803px] mx-auto"
              >
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Ask anything"
                  className="w-full h-[104px] pt-3 px-4 pb-10  pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-sm md:text-base shadow-lg"
                />
                <button
                  type="submit"
                  className="absolute right-3 top-1/2  transform -translate-y-1/2 w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800 transition-colors duration-200"
                >
                  <FaArrowUp className="w-4 h-4 " />
                </button>
              </form>
            </div>
          </>
        )}

        {/* MOBILE/TABLET: Fixed Bottom Section - contains carousel and input */}
        <div className="fixed bottom-0 left-0 right-0 bg-white">
          {messages.length === 0 && (
            /* MOBILE/TABLET: Suggestion cards carousel section */
            <div className="px-4 py-4 ">
              <div className="relative max-w-2xl mx-auto">
                <div className="overflow-hidden ">
                  <div
                    className="flex transition-transform duration-300 ease-in-out"
                    style={{
                      transform: isMobile
                        ? `translateX(-${currentSlide * 70}%)`
                        : `translateX(-${currentSlide * 100}%)`
                    }}
                    onTouchStart={handleTouchStart}
                    onTouchMove={handleTouchMove}
                    onTouchEnd={handleTouchEnd}
                  >
                    {isMobile
                      ? /* MOBILE: Show 1 full card + 1 half card per slide */
                        SUGGESTION_CARDS.map((card, index) => (
                          <div
                            key={index}
                            className="flex-shrink-0"
                            style={{
                              width: '70%',
                              marginRight: '4px'
                            }}
                          >
                            <div className="px-4">
                              <button
                                onClick={() => handleSuggestionClick(card.title)}
                                className="w-full p-5 rounded-[14px] bg-[#F6F6F6] text-center group hover:bg-[#D6EDFF] transition-all duration-200"
                              >
                                <div className="text-[14px] font-[600] text-black mb-1">
                                  {card.title}
                                </div>
                                <div className="text-[14px] text-gray-500">
                                  {card.subtitle}
                                </div>
                              </button>
                            </div>
                          </div>
                        ))
                      : /* TABLET: Show 2 cards per slide in grid layout */
                        Array.from({
                          length: Math.ceil(SUGGESTION_CARDS.length / 2),
                        }).map((_, slideIndex) => (
                          <div
                            key={slideIndex}
                            className="w-full flex-shrink-0 grid grid-cols-2 gap-3"
                          >
                            {SUGGESTION_CARDS.slice(
                              slideIndex * 2,
                              slideIndex * 2 + 2
                            ).map((card, cardIndex) => (
                              <button
                                key={slideIndex * 2 + cardIndex}
                                onClick={() =>
                                  handleSuggestionClick(card.title)
                                }
                                className="p-3 rounded-[14px] bg-gray-100 text-center group hover:bg-[#D6EDFF] transition-all duration-200"
                              >
                                <div className="text-[16px] font-[600] text-black mb-1">
                                  {card.title}
                                </div>
                                <div className="text-[16px] text-gray-500">
                                  {card.subtitle}
                                </div>
                              </button>
                            ))}
                          </div>
                        ))}
                  </div>
                </div>

                {/* TABLET ONLY: Navigation arrows - hidden on mobile, shown on tablet */}
                <button
                  onClick={handlePrevSlide}
                  className="hidden md:flex absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 z-10"
                >
                  <FaChevronLeft className="w-3 h-3 text-gray-600" />
                </button>
                <button
                  onClick={handleNextSlide}
                  className="hidden md:flex absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white border border-gray-200 rounded-full items-center justify-center hover:bg-gray-50 shadow-md transition-all duration-200 z-10"
                >
                  <FaChevronRight className="w-3 h-3 text-gray-600" />
                </button>
              </div>
            </div>
          )}

          {/* MOBILE/TABLET: Input form section - always visible at bottom */}
          <div className=" pt-2">
            <form onSubmit={handleSubmit} className="relative pb-5 p-3 ">
              <div className="relative w-full max-w-[890px] mx-auto">
                <input
                  ref={messages.length === 0 ? inputRef : null}
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Ask anything"
                  autoFocus={messages.length === 0}
                  className="w-full h-[104px] pt-3 px-4 pb-10 pr-12 text-black rounded-[20px] outline-none border border-[#E0E2D9] text-sm"
                />
                <button
                  type="submit"
                  className="absolute  right-3 -bottom-2 transform -translate-y-1/2 w-8 h-8 bg-black text-white rounded-full flex items-center justify-center hover:bg-gray-800"
                >
                  <FaArrowUp className="w-4 h-4" />
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;
